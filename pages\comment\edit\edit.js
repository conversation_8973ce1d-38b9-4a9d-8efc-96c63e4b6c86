/**
 * 评语编辑页面
 */
const { cloudService } = require('../../../services/cloudService');
const { notifyHomeDataUpdate, notifyCommentDataUpdate } = require('../../../utils/dataSync');
const { extractSurname } = require('../../../utils/globalUtils');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 评语信息
    commentInfo: {
      id: '',
      studentId: '',
      studentName: '',
      className: '',
      content: '',
      style: '',
      length: '',
      createTime: '',
      editTime: ''
    },

    // 表单数据
    formData: {
      content: '',
      style: 'warm',
      length: 'medium'
    },

    // 选择器当前索引
    currentStyleIndex: 1, // 默认选中'warm'
    currentLengthIndex: 1, // 默认选中'medium'

    // 选择器显示文本
    currentStyleText: '暖心观察型模板',
    currentLengthText: '标准版',

    // 选项配置
    styleOptions: [
      { value: 'wisdom', name: '智慧守护型模板', desc: '适合正式场合，用词规范' },
      { value: 'warm', name: '暖心观察型模板', desc: '语气温和，拉近师生距离' },
      { value: 'global', name: '全局分析型模板', desc: '积极正面，激发学生潜能' },
      { value: 'inductive', name: '归纳推理型模板', desc: '内容详实，分析深入' }
    ],

    lengthOptions: [
      { value: 'short', name: '简洁版', desc: '50-80字，简明扼要' },
      { value: 'medium', name: '标准版', desc: '80-150字，内容适中' },
      { value: 'long', name: '详细版', desc: '150-250字，内容丰富' }
    ],

    // 页面状态
    loading: true,
    saving: false,
    regenerating: false,

    // 字数统计
    wordCount: 0,
    maxWords: 500
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({ 'commentInfo.id': id });
      this.loadCommentDetail(id);
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载评语详情
   */
  async loadCommentDetail(commentId) {
    try {
      this.setData({ loading: true });
      const result = await cloudService.getCommentDetail(commentId);
      if (result.success) {
        const commentInfo = result.data;

        // 数据验证
        if (!commentInfo || !commentInfo._id) {
          throw new Error('评语数据格式错误');
        }

        const formData = {
          content: commentInfo.content || '',
          style: commentInfo.style || 'warm',
          length: commentInfo.length || 'medium'
        };

        // 计算选择器当前索引
        const currentStyleIndex = this.data.styleOptions.findIndex(opt => opt.value === formData.style);
        const currentLengthIndex = this.data.lengthOptions.findIndex(opt => opt.value === formData.length);

        // 获取显示文本
        const styleOption = this.data.styleOptions.find(opt => opt.value === formData.style);
        const lengthOption = this.data.lengthOptions.find(opt => opt.value === formData.length);
        const currentStyleText = styleOption ? styleOption.name : '暖心观察型模板';
        const currentLengthText = lengthOption ? lengthOption.name : '标准版';
        this.setData({
          commentInfo: {
            ...commentInfo,
            surname: extractSurname(commentInfo.studentName)
          },
          formData: formData,
          wordCount: (commentInfo.content || '').length,
          currentStyleIndex: currentStyleIndex >= 0 ? currentStyleIndex : 1,
          currentLengthIndex: currentLengthIndex >= 0 ? currentLengthIndex : 1,
          currentStyleText: currentStyleText,
          currentLengthText: currentLengthText
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: `编辑${commentInfo.studentName || '未知学生'}的评语`
        });
      } else {
        throw new Error(result.error || '加载失败');
      }
    } catch (error) {
      console.error('加载评语详情失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 内容输入变化
   */
  onContentChange(e) {
    const content = e.detail.value || '';
    const wordCount = content.length;

    this.setData({
      'formData.content': content,
      wordCount: wordCount
    });
  },

  /**
   * 风格选择
   */
  onStyleChange(e) {
    const index = e.detail.value;
    const styleOption = this.data.styleOptions[index];
    const style = styleOption.value;
    const styleText = styleOption.name;
    this.setData({
      'formData.style': style,
      currentStyleIndex: index,
      currentStyleText: styleText
    });
  },

  /**
   * 长度选择
   */
  onLengthChange(e) {
    const index = e.detail.value;
    const lengthOption = this.data.lengthOptions[index];
    const length = lengthOption.value;
    const lengthText = lengthOption.name;
    this.setData({
      'formData.length': length,
      currentLengthIndex: index,
      currentLengthText: lengthText
    });
  },

  /**
   * 重新生成评语
   */
  async regenerateComment() {
    const { commentInfo, formData } = this.data;

    wx.showModal({
      title: '确认重新生成',
      content: '重新生成将覆盖当前编辑的内容，确定继续吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            this.setData({ regenerating: true });

            wx.showLoading({
              title: 'AI重新生成中...',
              mask: true
            });

            const result = await cloudService.regenerateComment(commentInfo.id, {
              style: formData.style,
              length: formData.length
            });

            if (result.success) {
              const newContent = result.data.content;
              this.setData({
                'formData.content': newContent,
                wordCount: newContent.length
              });

              wx.showToast({
                title: '重新生成成功',
                icon: 'success'
              });
            } else {
              throw new Error(result.error || '重新生成失败');
            }
          } catch (error) {
            console.error('重新生成评语失败:', error);
            wx.showToast({
              title: '重新生成失败',
              icon: 'none'
            });
          } finally {
            wx.hideLoading();
            this.setData({ regenerating: false });
          }
        }
      }
    });
  },

  /**
   * 保存评语
   */
  async saveComment() {
    const { commentInfo, formData } = this.data;

    // 验证内容
    if (!formData.content.trim()) {
      wx.showToast({
        title: '请输入评语内容',
        icon: 'none'
      });
      return;
    }

    if (formData.content.length > this.data.maxWords) {
      wx.showToast({
        title: `评语内容不能超过${this.data.maxWords}字`,
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ saving: true });

      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      const updateData = {
        content: formData.content.trim(),
        style: formData.style,
        length: formData.length,
        editTime: new Date().toISOString()
      };

      const result = await cloudService.updateComment(commentInfo.id, updateData);

      if (result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 通知数据更新
        notifyHomeDataUpdate();
        notifyCommentDataUpdate();

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      } else {
        throw new Error(result.error || '保存失败');
      }
    } catch (error) {
      console.error('保存评语失败:', error);
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
      this.setData({ saving: false });
    }
  },

  /**
   * 取消编辑
   */
  onCancel() {
    const { commentInfo, formData } = this.data;

    // 检查是否有修改
    if (formData.content !== commentInfo.content || 
        formData.style !== commentInfo.style || 
        formData.length !== commentInfo.length) {
      wx.showModal({
        title: '确认取消',
        content: '您有未保存的修改，确定要取消吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 获取风格文本
   */
  getStyleText(style) {
    const option = this.data.styleOptions.find(opt => opt.value === style);
    const result = option ? option.name : '未知风格';
    return result;
  },

  /**
   * 获取长度文本
   */
  getLengthText(length) {
    const option = this.data.lengthOptions.find(opt => opt.value === length);
    const result = option ? option.name : '未知长度';
    return result;
  }
}); 