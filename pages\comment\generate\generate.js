/**
 * AI评语生成页面
 * 基于学生行为记录数据智能生成个性化评语
 */
const app = getApp();
const aiService = require('../../../utils/aiService');
const { validateCommentParams, sanitizeData } = require('../../../utils/validation');
const { showLoading, hideLoading, showSuccess, showError, showConfirm } = require('../../../utils/globalUtils');
const { usageController } = require('../../../utils/usageController');
// 安全加载智能分享功能
let smartShare = null;
let emergencyShare = null;
try {
  const shareModule = require('../../../utils/smartShare');
  smartShare = shareModule.smartShare;

} catch (error) {

  try {
    const emergencyModule = require('../../../utils/emergencyShare');
    emergencyShare = emergencyModule.emergencyAutoShare;

  } catch (emergencyError) {
    console.error('[分享模块] 所有分享模块加载失败:', emergencyError);
  }
}

// 获取云服务实例的辅助函数
function getCloudService() {
  try {
    // 优先从全局获取
    if (typeof global !== 'undefined' && global.getCloudService) {
      return global.getCloudService();
    }

    // 备用方案：从app获取
    const cloudService = app.globalData.cloudService;
    if (!cloudService) {
      throw new Error('云服务未初始化，请稍后重试');
    }
    return cloudService;
  } catch (error) {
    console.error('获取云服务失败:', error);
    throw new Error('云服务不可用，请检查网络连接后重试');
  }
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 学生相关
    selectedStudents: [],
    selectedStudentIds: [],
    allStudents: [],
    filteredStudents: [],
    studentSearchKeyword: '',
    showStudentPopup: false,

    // 弹窗状态管理
    isPopupOperating: false,    // 弹窗操作锁，防止重复操作
    isPageInitialized: false,   // 页面初始化标记

    // 班级筛选
    classList: [],
    selectedClassId: '',

    // 表单数据
    formData: {
      style: 'warm',           // 评语风格
      length: 'medium',        // 评语长度
      startDate: null,         // 开始日期
      endDate: null,           // 结束日期
      startDateText: '',       // 开始日期文本
      endDateText: '',         // 结束日期文本
      focus: [],               // 关注重点
      customRequirement: '',   // 自定义要求
      includeAdvice: true,     // 是否包含建议
      includeEncouragement: true // 是否包含鼓励
    },

    // 原型图相关数据
    progressWidth: 0,
    showResult: false,
    generatedComment: '',
    resultScore: '9.2',

    // 选项配置（与设置页面保持一致）
    styleOptions: [
      { value: 'warm', name: '暖心观察型模板', desc: '语气温和，拉近师生距离', emoji: '🤗' },
      { value: 'wisdom', name: '智慧守护型模板', desc: '适合正式场合，用词规范', emoji: '📋' },
      { value: 'global', name: '全局分析型模板', desc: '积极正面，激发学生潜能', emoji: '💪' },
      { value: 'inductive', name: '归纳推理型模板', desc: '内容详实，分析深入', emoji: '🔍' }
    ],

    lengthOptions: [
      { value: 'short', name: '简洁版', desc: '50-80字，简明扼要' },
      { value: 'medium', name: '标准版', desc: '80-150字，内容适中' },
      { value: 'long', name: '详细版', desc: '150-250字，内容丰富' }
    ],

    focusOptions: [
      { value: 'academic', name: '学习表现', icon: 'edit' },
      { value: 'behavior', name: '行为习惯', icon: 'like-o' },
      { value: 'social', name: '社交能力', icon: 'friends-o' },
      { value: 'creativity', name: '创新思维', icon: 'bulb-o' },
      { value: 'leadership', name: '领导力', icon: 'medal' },
      { value: 'teamwork', name: '团队合作', icon: 'cluster-o' }
    ],

    // 生成状态
    generating: false,
    generateProgress: {
      current: 0,
      total: 0,
      currentStudent: '',
      status: ''
    },

    // 生成结果
    generateResult: {
      successCount: 0,
      failCount: 0,
      comments: []
    },

    // 弹窗状态
    showSuccessPopup: false,

    // 保存状态
    saving: false,

    // 页面状态
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

    // 确保弹窗初始状态为关闭
    this.setData({
      showStudentPopup: false
    });

    // 处理重新生成评语的参数
    if (options.mode === 'regenerate') {
      this.handleRegenerateMode(options);
    } else {
      this.initPage();
    }
  },

  /**
   * 处理重新生成评语模式
   */
  async handleRegenerateMode(options) {

    try {
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: '重新生成评语'
      });

      // 预设学生信息和生成参数
      const studentInfo = {
        id: options.studentId,
        name: decodeURIComponent(options.studentName || ''),
        className: decodeURIComponent(options.className || '')
      };

      // 获取用户偏好设置作为默认值
      const aiConfig = wx.getStorageSync('aiConfig') || {};
      const userPreferredStyle = aiConfig.commentStyle || 'warm';

      // 设置生成参数 - 优先使用原始样式，如果没有则使用用户偏好
      const generateParams = {
        style: options.originalStyle || userPreferredStyle,
        length: options.originalLength || 'medium'
      };

      // 更新页面数据 - 修复数据结构匹配问题
      this.setData({
        // 学生选择相关
        selectedStudents: [studentInfo],
        selectedStudentIds: [studentInfo.id],

        // 表单数据
        'formData.style': generateParams.style,
        'formData.length': generateParams.length,

        // 重新生成模式标识
        isRegenerateMode: true,
        originalCommentId: options.commentId,
        showStudentPopup: false // 不显示学生选择弹窗
      });

      // 显示重新生成提示
      wx.showToast({
        title: `为${studentInfo.name}重新生成评语`,
        icon: 'none',
        duration: 2000
      });

      // 初始化其他数据
      await this.initPage();

    } catch (error) {
      console.error('处理重新生成模式失败:', error);
      wx.showToast({
        title: '参数解析失败',
        icon: 'none'
      });
      // 降级到普通模式
      this.initPage();
    }
  },

  /**
   * 读取用户AI配置偏好
   */
  loadUserAIPreferences() {
    try {

      // 从本地存储读取AI配置
      const aiConfig = wx.getStorageSync('aiConfig') || {};

      // 应用用户的评语风格偏好
      if (aiConfig.commentStyle) {

        this.setData({
          'formData.style': aiConfig.commentStyle
        });
        
        // 显示风格应用提示
        if (aiConfig.commentStyleName) {
          wx.showToast({
            title: `已应用${aiConfig.commentStyleName}风格`,
            icon: 'none',
            duration: 1500
          });
        }
      }

    } catch (error) {
      console.error('读取用户AI偏好失败:', error);
      // 使用默认设置，不影响正常使用
    }
  },

  /**
   * 初始化页面
   */
  async initPage() {

    try {
      // 立即加载真实数据，确保页面有内容显示
      await this.loadRealData();

      this.setData({
        loading: true
        // 移除强制关闭弹窗，避免干扰用户操作
        // showStudentPopup: false
      });

      // 设置默认时间范围（最近一个月）
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 1);

      // 读取用户AI配置偏好
      this.loadUserAIPreferences();

      this.setData({
        'formData.startDate': startDate.getTime(),
        'formData.endDate': endDate.getTime(),
        'formData.startDateText': this.formatDate(startDate),
        'formData.endDateText': this.formatDate(endDate)
      });

      // 尝试加载真实数据（可选）
      try {
        await this.loadClassList();
        await this.loadStudentList();
        // 数据加载成功
      } catch (dataError) {
        // 使用默认数据
      }

      // 初始化完成

    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '初始化失败，使用测试数据',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false
        // 移除强制关闭弹窗，避免干扰用户操作
        // showStudentPopup: false
      });
    }
  },

  /**
   * 加载真实数据
   */
  async loadRealData() {
    try {
      // 先加载班级数据
      await this.loadClassList();
      
      // 再加载学生数据
      await this.loadStudentList();

    } catch (error) {
      console.error('加载真实数据失败:', error);
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 加载班级列表
   */
  async loadClassList() {
    try {
      const cloudService = getCloudService();
      const result = await cloudService.getClassList();
      if (result.success && result.data && result.data.length > 0) {
        const classList = [
          { id: '', name: '全部班级' },
          ...result.data.map(item => ({
            id: item._id,
            name: item.className
          }))
        ];
        this.setData({ classList });

      } else {
        // 只设置"全部班级"选项
        this.setData({
          classList: [{ id: '', name: '全部班级' }]
        });

      }
    } catch (error) {
      console.error('加载班级列表失败:', error);
      // 只设置"全部班级"选项
      this.setData({
        classList: [{ id: '', name: '全部班级' }]
      });
    }
  },

  /**
   * 提取学生姓氏（中文姓名的第一个字符）
   */
  extractSurname(fullName) {
    if (!fullName || fullName.trim() === '') return '学';
    return fullName.charAt(0);
  },

  /**
   * 加载学生列表
   */
  async loadStudentList() {
    try {

      const cloudService = getCloudService();

      if (!cloudService) {
        throw new Error('云服务不可用');
      }

      const result = await cloudService.getStudentList();

      if (result && result.success && Array.isArray(result.data)) {
        const allStudents = result.data.map(item => ({
          id: String(item._id || item.id), // 确保ID为字符串类型
          name: item.name || '',
          className: item.className || '',
          studentNumber: item.studentNumber || item.studentId || '',
          avatar: item.avatar || '',
          surname: this.extractSurname(item.name || '')
        }));

        this.setData({
          allStudents,
          filteredStudents: allStudents,
          showEmptyState: allStudents.length === 0
        });

        return { success: true, data: allStudents };
      } else {
        // 如果没有数据，显示空状态

        this.setData({
          allStudents: [],
          filteredStudents: [],
          showEmptyState: true
        });
        return { success: false, error: '没有学生数据' };
      }
    } catch (error) {
      console.error('加载学生列表失败:', error);
      // 加载失败时显示错误状态
      this.setData({
        allStudents: [],
        filteredStudents: [],
        showEmptyState: true,
        errorMessage: '加载学生数据失败，请检查网络连接'
      });
      return { success: false, error: error.message };
    }
  },

  /**
   * 关闭成功弹窗
   */
  closeSuccessPopup() {
    this.setData({ showSuccessPopup: false });
  },

  /**
   * 查看生成的评语
   */
  viewGeneratedComments() {
    const { generateResult, formData } = this.data;

    // 修复数据结构：将content字段转换为comment字段以匹配预览页面期望
    const formattedComments = generateResult.comments.map(item => ({
      ...item,
      comment: item.content, // 将content字段映射为comment字段
      // 保留原有content字段以备兼容
      content: item.content
    }));

    // 准备跳转数据
    const jumpData = {
      comments: formattedComments,
      totalCount: generateResult.comments.length,
      successCount: generateResult.successCount,
      generateInfo: {
        style: formData.style,
        styleText: this.getStyleText(formData.style),
        length: formData.length,
        lengthText: this.getLengthText(formData.length),
        generateTime: new Date().toLocaleString()
      }
    };

    // 关闭弹窗
    this.setData({ showSuccessPopup: false });

    // 跳转到预览页面
    wx.navigateTo({
      url: `/pages/comment/preview/preview?data=${encodeURIComponent(JSON.stringify(jumpData))}`
    });
  },

  /**
   * 计算评语质量评分
   */
  calculateCommentScore(content) {
    if (!content || typeof content !== 'string') {
      return 7.0; // 默认评分
    }

    let score = 7.0; // 基础分
    const length = content.length;

    // 长度评分 (20%)
    if (length >= 100) {
      score += 1.0; // 内容充实
    } else if (length >= 50) {
      score += 0.5; // 内容适中
    }

    // 内容质量评分 (30%)
    const qualityWords = ['积极', '认真', '努力', '优秀', '进步', '表现', '突出', '良好', '出色', '杰出'];
    const qualityMatches = qualityWords.filter(word => content.includes(word)).length;
    score += Math.min(qualityMatches * 0.2, 1.0);

    // 结构评分 (20%)
    if (content.includes('在') && content.includes('方面')) {
      score += 0.3; // 有具体方面描述
    }
    if (content.includes('希望') || content.includes('建议') || content.includes('继续')) {
      score += 0.3; // 有建设性建议
    }

    // 语言丰富度 (30%)
    const sentences = content.split(/[。！？]/).filter(s => s.trim().length > 0);
    if (sentences.length >= 3) {
      score += 0.5; // 多句表达
    }

    // 确保评分在合理范围内
    score = Math.min(Math.max(score, 6.0), 10.0);
    
    // 保留一位小数
    return Math.round(score * 10) / 10;
  },

  /**
   * 直接保存评语
   */
  async saveDirectly() {
    const { generateResult } = this.data;

    if (generateResult.comments.length === 0) {
      showError('没有可保存的评语');
      return;
    }

    this.setData({ saving: true });
    showLoading('保存中...');

    try {
      // 准备要保存的评语数据（标准化格式）
      const commentsToSave = generateResult.comments.map(comment => {
        // 计算评语质量评分
        const score = this.calculateCommentScore(comment.content || comment.comment);
        
        return {
          id: comment.id || `direct_saved_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          studentId: comment.studentId,
          studentName: comment.studentName,
          className: comment.className,
          content: comment.content || comment.comment,
          score: score, // 添加评分
          style: this.data.formData.style,
          length: this.data.formData.length,
          focus: this.data.formData.focus,
          timeRange: {
            startDate: this.data.formData.startDate,
            endDate: this.data.formData.endDate
          },
          analysis: comment.analysis,
          recordCount: comment.recordCount,
          createTime: new Date().toISOString() // 添加创建时间
        };
      });
      
      // 优先保存到云数据库
      const cloudService = getCloudService();
      const result = await cloudService.batchSaveComments(commentsToSave);
      
      if (result.success) {

      } else {

        // 降级到本地存储
        const existingSavedComments = wx.getStorageSync('savedComments') || [];
        const updatedSavedComments = [...existingSavedComments, ...commentsToSave];
        wx.setStorageSync('savedComments', updatedSavedComments);
      }

      hideLoading();
      
      // 简洁处理：只显示保存成功toast，不自动跳转
      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 2000
      });

      // 保存成功后，标记已保存状态
      this.setData({
        commentsSaved: true,
        'generateResult.saved': true
      });

    } catch (error) {
      hideLoading();
      console.error('保存失败:', error);
      showError('保存失败');
    } finally {
      this.setData({ saving: false });
    }
  },

  /**
   * 获取风格文本
   */
  getStyleText(style) {
    const styleTexts = {
      warm: '暖心观察型',
      wisdom: '智慧守护型',
      global: '全局分析型',
      inductive: '归纳推理型'
    };
    return styleTexts[style] || '暖心观察型';
  },

  /**
   * 获取长度文本
   */
  getLengthText(length) {
    const lengthTexts = {
      short: '简洁',
      medium: '适中',
      long: '详细'
    };
    return lengthTexts[length] || '适中';
  },

  /**
   * 阻止弹窗关闭
   */
  preventClose() {
    // 阻止事件冒泡，防止点击弹窗内容时关闭
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 表单选项变化
   */
  onStyleChange(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'formData.style': value
    });
  },

  onLengthChange(e) {
    const { value } = e.currentTarget.dataset;
    this.setData({
      'formData.length': value
    });
  },

  onFocusChange(e) {
    const { value } = e.currentTarget.dataset;
    const focus = [...this.data.formData.focus];
    const index = focus.indexOf(value);

    if (index > -1) {
      focus.splice(index, 1);
    } else {
      focus.push(value);
    }

    this.setData({
      'formData.focus': focus
    });
  },

  /**
   * 清空所有选中学生
   */
  clearAllStudents() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有已选择的学生吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            selectedStudents: [],
            selectedStudentIds: []
          });
        }
      }
    });
  },

  /**
   * 班级筛选
   */
  onClassFilter(e) {
    const classId = e.detail;
    this.setData({ selectedClassId: classId });
    this.filterStudents();
  },

  /**
   * 学生搜索
   */
  onStudentSearch(e) {
    const keyword = e.detail.value;
    this.setData({ studentSearchKeyword: keyword });
    this.filterStudents();
  },

  /**
   * 筛选学生列表
   */
  filterStudents() {
    let filtered = [...this.data.allStudents];

    // 按班级筛选
    if (this.data.selectedClassId) {
      filtered = filtered.filter(student => student.classId === this.data.selectedClassId);
    }

    // 按关键词搜索
    if (this.data.studentSearchKeyword) {
      const keyword = this.data.studentSearchKeyword.toLowerCase();
      filtered = filtered.filter(student =>
        student.name.toLowerCase().includes(keyword) ||
        (student.studentNumber && student.studentNumber.includes(keyword)) ||
        (student.className && student.className.toLowerCase().includes(keyword))
      );
    }

    this.setData({ filteredStudents: filtered });
  },

  onCustomRequirementChange(e) {
    this.setData({
      'formData.customRequirement': e.detail.value
    });
  },

  onIncludeAdviceChange(e) {
    this.setData({
      'formData.includeAdvice': e.detail.value
    });
  },

  onIncludeEncouragementChange(e) {
    this.setData({
      'formData.includeEncouragement': e.detail.value
    });
  },

  /**
   * 生成AI评语（分批处理版）
   */
  async generateComments() {
    try {
      // 检查使用限制
      const canUse = await usageController.beforeGenerate();
      if (!canUse) {
        return; // usageController.beforeGenerate()内部已处理提示
      }

      // 数据验证
      const validationResult = this.validateGenerateParams();
      if (!validationResult.isValid) {
        showError(validationResult.message);
        return;
      }

      const { selectedStudents, formData } = this.data;
      const totalCount = selectedStudents.length;

      // 检查是否有学生缺少行为记录，需要用户授权使用通用评语
      const studentsWithoutRecords = await this.checkStudentsWithoutRecords(selectedStudents);
      if (studentsWithoutRecords.length > 0) {
        const userAgreed = await this.showGenericCommentAuthModal(studentsWithoutRecords);
        if (!userAgreed) {
          showError('用户取消操作');
          return;
        }
      }

      // 初始化生成状态
      this.setData({
        generating: true,
        progressWidth: 0,
        generateProgress: {
          current: 0,
          total: totalCount,
          currentStudent: '',
          status: '准备开始生成评语...'
        },
        generateResult: {
          successCount: 0,
          failCount: 0,
          comments: []
        }
      });

      hideLoading();

      // 开始分批生成
      await this.batchGenerateComments(selectedStudents, formData);

    } catch (error) {
      console.error('生成评语失败:', error);
      showError('生成失败，请重试');
      this.setData({ generating: false });
    }
  },

  /**
   * 批量生成评语 - 优化并发控制和重试机制
   */
  async batchGenerateComments(students, formData) {
    const results = [];
    let successCount = 0;
    let failCount = 0;
    
    // 并发控制：每次最多同时处理3个学生
    const CONCURRENT_LIMIT = 3;
    const MAX_RETRIES = 2; // 每个学生最多重试2次

    // 将学生分组，每组CONCURRENT_LIMIT个
    const studentBatches = [];
    for (let i = 0; i < students.length; i += CONCURRENT_LIMIT) {
      studentBatches.push(students.slice(i, i + CONCURRENT_LIMIT));
    }

    // 逐批处理
    for (let batchIndex = 0; batchIndex < studentBatches.length; batchIndex++) {
      const batch = studentBatches[batchIndex];

      // 并行处理当前批次的学生
      const batchPromises = batch.map(async (student, studentIndex) => {
        const globalIndex = batchIndex * CONCURRENT_LIMIT + studentIndex;
        
        // 更新进度
        const progressPercent = Math.round(((globalIndex + 1) / this.data.generateProgress.total) * 100);
        this.setData({
          'generateProgress.current': globalIndex + 1,
          'generateProgress.currentStudent': student.name,
          'generateProgress.status': `正在生成 ${student.name} 的评语...`,
          progressWidth: progressPercent
        });

        // 重试机制
        for (let attempt = 0; attempt <= MAX_RETRIES; attempt++) {
          try {

            const comment = await this.generateAIComment(student, formData);

            return {
              success: true,
              student: student,
              comment: comment
            };

          } catch (error) {
            console.error(`${student.name}第${attempt + 1}次生成失败:`, error.message);
            
            if (attempt < MAX_RETRIES) {
              // 重试前等待更长时间
              await new Promise(resolve => setTimeout(resolve, 2000 * (attempt + 1)));

            } else {
              // 最后一次尝试失败
              console.error(`${student.name}所有重试均失败`);
              return {
                success: false,
                student: student,
                error: error.message
              };
            }
          }
        }
      });

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises);
      
      // 处理批次结果
      batchResults.forEach(result => {
        if (result.success) {
          results.push(result.comment);
          successCount++;
        } else {
          failCount++;
          // 为失败的学生创建错误记录
          results.push({
            id: `error_${Date.now()}_${Math.random()}`,
            studentId: result.student.id,
            studentName: result.student.name,
            className: result.student.className,
            error: true,
            content: `生成失败：${result.error}`,
            style: formData.style,
            generateTime: new Date().toLocaleString()
          });
        }
      });

      // 批次间等待，避免API限流
      if (batchIndex < studentBatches.length - 1) {

        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // 记录AI使用次数（如果有成功生成的评语）
    if (successCount > 0) {
      usageController.afterGenerate({
        studentCount: successCount,
        generateTime: Date.now()
      });
    }

    // 生成完成，显示结果弹窗
    this.setData({
      generating: false,
      progressWidth: 100,
      'generateProgress.status': `生成完成：成功${successCount}个，失败${failCount}个`,
      generateResult: {
        successCount,
        failCount,
        comments: results
      },
      showSuccessPopup: true
    });

    // 延迟重置进度条
    setTimeout(() => {
      this.setData({
        progressWidth: 0
      });
    }, 2000);

    // 提示用户是否立即保存（生产环境建议）
    if (successCount > 0) {
      setTimeout(() => {
        wx.showModal({
          title: '生成完成',
          content: `成功生成${successCount}条评语，是否立即保存到云数据库？`,
          confirmText: '立即保存',
          cancelText: '稍后保存',
          success: (res) => {
            if (res.confirm) {
              this.saveAndShowResult();
            }
          }
        });
      }, 1000);
    }
  },

  /**
   * 保存评语并显示查看选项
   */
  async saveAndShowResult() {
    const { generateResult } = this.data;

    if (generateResult.comments.length === 0) {
      showError('没有可保存的评语');
      return;
    }

    this.setData({ saving: true });
    showLoading('保存中...');

    try {
      // 准备要保存的评语数据（标准化格式）
      const commentsToSave = generateResult.comments.map(comment => {
        // 计算评语质量评分
        const score = this.calculateCommentScore(comment.content || comment.comment);
        
        return {
          id: comment.id || `direct_saved_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          studentId: comment.studentId,
          studentName: comment.studentName,
          className: comment.className,
          content: comment.content || comment.comment,
          score: score, // 添加评分
          style: this.data.formData.style,
          length: this.data.formData.length,
          focus: this.data.formData.focus,
          timeRange: {
            startDate: this.data.formData.startDate,
            endDate: this.data.formData.endDate
          },
          analysis: comment.analysis,
          recordCount: comment.recordCount,
          createTime: new Date().toISOString() // 添加创建时间
        };
      });
      
      // 优先保存到云数据库
      const cloudService = getCloudService();
      const result = await cloudService.batchSaveComments(commentsToSave);
      
      if (result.success) {

      } else {

        // 降级到本地存储
        const existingSavedComments = wx.getStorageSync('savedComments') || [];
        const updatedSavedComments = [...existingSavedComments, ...commentsToSave];
        wx.setStorageSync('savedComments', updatedSavedComments);
      }

      hideLoading();
      
      // 简洁处理：只显示保存成功toast，不自动跳转
      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 2000
      });
      
      // 标记保存状态
      this.setData({
        commentsSaved: true,
        'generateResult.saved': true
      });

    } catch (error) {
      hideLoading();
      console.error('保存失败:', error);
      showError('保存失败');
    } finally {
      this.setData({ saving: false });
    }
  },

  /**
   * 评语操作方法
   */
  onCommentSelect(e) {
    const { index } = e.currentTarget.dataset;
    const generatedComments = [...this.data.generatedComments];
    generatedComments[index].selected = !generatedComments[index].selected;

    this.setData({ generatedComments });
  },

  editComment(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.generatedComments[index];

    wx.showModal({
      title: '编辑评语',
      content: comment.comment,
      editable: true,
      success: (res) => {
        if (res.confirm && res.content) {
          const generatedComments = [...this.data.generatedComments];
          generatedComments[index].comment = res.content;
          this.setData({ generatedComments });
        }
      }
    });
  },

  regenerateComment(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.generatedComments[index];

    wx.showModal({
      title: '重新生成',
      content: `确定要为${comment.studentName}重新生成评语吗？`,
      success: async (res) => {
        if (res.confirm) {
          await this.regenerateSingleComment(index);
        }
      }
    });
  },

  async regenerateSingleComment(index) {
    const comment = this.data.generatedComments[index];
    const { formData } = this.data;

    try {
      wx.showLoading({
        title: '重新生成中...',
        mask: true
      });

              const cloudService = getCloudService();
        const result = await cloudService.generateAIComment(comment.studentId, {
        style: formData.style,
        length: formData.length,
        startDate: formData.startDate,
        endDate: formData.endDate,
        focus: formData.focus,
        customRequirement: formData.customRequirement,
        includeAdvice: formData.includeAdvice,
        includeEncouragement: formData.includeEncouragement
      });

      if (result.success) {
        const generatedComments = [...this.data.generatedComments];
        generatedComments[index] = {
          ...result.data,
          id: comment.id,
          selected: comment.selected
        };

        this.setData({ generatedComments });

        wx.showToast({
          title: '重新生成成功',
          icon: 'success'
        });
      } else {
        throw new Error(result.error || '重新生成失败');
      }

    } catch (error) {
      console.error('重新生成评语失败:', error);
      wx.showToast({
        title: '重新生成失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 保存评语
   */
  async saveComments() {
    const selectedComments = this.data.generatedComments.filter(comment =>
      comment.selected && !comment.error
    );

    if (selectedComments.length === 0) {
      wx.showToast({
        title: '请选择要保存的评语',
        icon: 'none'
      });
      return;
    }

    this.setData({ saving: true });

    try {
      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      // 准备保存的数据
      const commentsToSave = selectedComments.map(comment => ({
        id: comment.id, // 保持原有的ID
        studentId: comment.studentId,
        studentName: comment.studentName,
        className: comment.className,
        content: comment.comment,
        style: this.data.formData.style,
        length: this.data.formData.length,
        focus: this.data.formData.focus,
        timeRange: {
          startDate: this.data.formData.startDate,
          endDate: this.data.formData.endDate
        },
        analysis: comment.analysis,
        recordCount: comment.recordCount
      }));

      // 批量保存
      const cloudService = getCloudService();
      const result = await cloudService.batchSaveComments(commentsToSave);

      if (result.success) {
        wx.hideLoading();
        wx.showModal({
          title: '保存成功',
          content: `成功保存${selectedComments.length}条评语`,
          showCancel: false,
          success: () => {
            // 跳转到评语列表页面
            wx.switchTab({
              url: '/pages/works/list/list'
            });
          }
        });
      } else {
        throw new Error(result.error || '保存失败');
      }

    } catch (error) {
      console.error('保存评语失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  /**
   * 全选/取消全选
   */
  toggleSelectAll() {
    const generatedComments = [...this.data.generatedComments];
    const hasUnselected = generatedComments.some(comment => !comment.selected && !comment.error);

    generatedComments.forEach(comment => {
      if (!comment.error) {
        comment.selected = hasUnselected;
      }
    });

    this.setData({ generatedComments });
  },

  /**
   * 清空结果
   */
  clearResults() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有生成的评语吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            generatedComments: []
          });
        }
      }
    });
  },

  /**
   * 预览评语
   */
  previewComment(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.generatedComments[index];

    wx.showModal({
      title: comment.studentName,
      content: comment.comment,
      showCancel: false,
      confirmText: '关闭'
    });
  },

  /**
   * 显示学生选择弹窗
   */
  showStudentPicker() {

    // 防抖处理：如果弹窗正在显示或操作中，直接返回
    if (this.data.showStudentPopup || this.data.isPopupOperating) {

      return;
    }

    // 设置操作锁，防止重复操作
    this.setData({ isPopupOperating: true });

    // 检查是否有学生数据，如果没有则先加载
    if (this.data.allStudents.length === 0) {

      this.loadStudentList().then(() => {
        // 加载完成后再次尝试显示弹窗
        if (this.data.allStudents.length === 0) {
          wx.showToast({
            title: '暂无学生数据，请先添加学生',
            icon: 'none'
          });
          this.setData({ isPopupOperating: false });
          return;
        }
        this.doShowStudentPicker();
      }).catch(error => {
        console.error('加载学生数据失败:', error);
        wx.showToast({
          title: '加载学生数据失败',
          icon: 'none'
        });
        this.setData({ isPopupOperating: false });
      });
      return;
    }

    this.doShowStudentPicker();
  },

  /**
   * 执行显示学生选择弹窗
   */
  doShowStudentPicker() {
    try {

      // 为学生数据添加选中状态
      const studentsWithSelection = this.data.allStudents.map(student => ({
        ...student,
        isSelected: this.data.selectedStudentIds.includes(String(student.id))
      }));

      // 使用 setTimeout 确保状态更新的时序正确
      setTimeout(() => {
        // 记录弹窗显示时间，用于防止意外关闭
        this.popupShowTime = Date.now();

        this.setData({
          showStudentPopup: true,
          filteredStudents: studentsWithSelection,
          studentSearchKeyword: '',
          isPopupOperating: false // 释放操作锁
        });

      }, 50); // 50ms 延迟确保事件处理完成

    } catch (error) {
      console.error('显示学生选择弹窗失败:', error);
      this.setData({ isPopupOperating: false }); // 释放操作锁
      wx.showToast({
        title: '显示弹窗失败',
        icon: 'none'
      });
    }
  },

  /**
   * 隐藏学生选择弹窗
   */
  hideStudentPicker() {

    // 防止重复关闭
    if (!this.data.showStudentPopup) {

      return;
    }

    this.setData({
      showStudentPopup: false,
      isPopupOperating: false // 确保释放操作锁
    });

  },

  /**
   * 安全的遮罩层点击处理 - 优化防止意外触发
   */
  onMaskTap() {

    // 检查弹窗是否正在操作中，如果是则忽略点击
    if (this.data.isPopupOperating) {

      return;
    }

    // 检查弹窗显示时间，防止刚显示就被意外关闭
    const now = Date.now();
    if (!this.popupShowTime || (now - this.popupShowTime) < 300) {

      return;
    }

    if (this.data.showStudentPopup) {

      this.hideStudentPicker();
    }
  },

  /**
   * 防止点击弹窗内容时关闭
   */
  preventClose() {
    // 空方法，防止事件冒泡

  },

  /**
   * 弹窗内全选当前显示的学生
   */
  selectAllInPopup() {
    const { filteredStudents, selectedStudentIds } = this.data;

    if (filteredStudents.length === 0) {
      wx.showToast({
        title: '暂无可选择的学生',
        icon: 'none'
      });
      return;
    }

    // 确保ID类型一致性
    const currentSelectedIds = selectedStudentIds.map(id => String(id));

    // 获取当前筛选结果中未选择的学生
    const unselectedStudents = filteredStudents.filter(student =>
      !currentSelectedIds.includes(String(student.id))
    );

    if (unselectedStudents.length === 0) {
      wx.showToast({
        title: '当前显示的学生已全部选择',
        icon: 'none'
      });
      return;
    }

    // 合并已选择的学生和新选择的学生
    const newSelectedStudents = [...this.data.selectedStudents, ...unselectedStudents];
    const newSelectedStudentIds = [...currentSelectedIds, ...unselectedStudents.map(s => String(s.id))];

    // 更新filteredStudents中的isSelected属性
    const updatedFilteredStudents = filteredStudents.map(student => ({
      ...student,
      isSelected: true
    }));

    this.setData({
      selectedStudents: newSelectedStudents,
      selectedStudentIds: newSelectedStudentIds,
      filteredStudents: updatedFilteredStudents
    });

    wx.showToast({
      title: `已添加${unselectedStudents.length}个学生`,
      icon: 'success'
    });
  },

  /**
   * 弹窗内取消全选
   */
  unselectAllInPopup() {
    if (this.data.selectedStudentIds.length === 0) {
      wx.showToast({
        title: '暂无已选择的学生',
        icon: 'none'
      });
      return;
    }

    // 更新filteredStudents中的isSelected属性
    const updatedFilteredStudents = this.data.filteredStudents.map(student => ({
      ...student,
      isSelected: false
    }));

    this.setData({
      selectedStudents: [],
      selectedStudentIds: [],
      filteredStudents: updatedFilteredStudents
    });

    wx.showToast({
      title: '已取消全部选择',
      icon: 'success'
    });
  },

  /**
   * 继续添加学生（已有生成结果时）
   */
  continueGenerate() {
    this.showStudentPicker();
  },

  /**
   * 重新生成全部评语
   */
  regenerateAll() {
    if (this.data.generatedComments.length === 0) {
      wx.showToast({
        title: '暂无评语需要重新生成',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '重新生成',
      content: `确定要重新生成全部${this.data.generatedComments.length}条评语吗？原有评语将被覆盖。`,
      success: (res) => {
        if (res.confirm) {
          // 清空现有结果
          this.setData({
            generatedComments: []
          });
          
          // 重新生成
          setTimeout(() => {
            this.generateComments();
          }, 300);
        }
      }
    });
  },

  /**
   * 学生搜索 - 实时筛选
   */
  onStudentSearchChange(e) {
    const keyword = e.detail.value || '';

    this.filterStudents(keyword);
  },

  /**
   * 搜索清除
   */
  onStudentSearchClear() {
    this.filterStudents('');
  },

  /**
   * 筛选学生 - 支持姓名和班级模糊匹配
   */
  filterStudents(keyword) {

    let filtered;
    if (!keyword || keyword.trim() === '') {
      // 无关键词时显示所有学生
      filtered = this.data.allStudents;
    } else {
      const searchKeyword = keyword.trim().toLowerCase();

      filtered = this.data.allStudents.filter(student => {
        // 姓名匹配（支持中文）
        const nameMatch = student.name && student.name.toLowerCase().includes(searchKeyword);
        // 班级匹配
        const classMatch = student.className && student.className.toLowerCase().includes(searchKeyword);
        // 学号匹配
        const numberMatch = student.studentNumber && student.studentNumber.includes(searchKeyword);

        return nameMatch || classMatch || numberMatch;
      });
    }

    // 为筛选后的学生添加选中状态
    const filteredWithSelection = filtered.map(student => ({
      ...student,
      isSelected: this.data.selectedStudentIds.includes(String(student.id))
    }));

    this.setData({
      studentSearchKeyword: keyword || '',
      filteredStudents: filteredWithSelection
    });

    // 输出匹配的学生名字用于调试
    if (filtered.length > 0) {
      
    }
  },

  /**
   * 全选/取消全选学生
   */
  toggleSelectAll() {
    const { filteredStudents, selectedStudentIds } = this.data;

    // 确保ID类型一致性
    const currentSelectedIds = selectedStudentIds.map(id => String(id));
    const allSelected = filteredStudents.every(student =>
      currentSelectedIds.includes(String(student.id))
    );

    if (allSelected) {
      // 取消全选
      const filteredStudentIds = filteredStudents.map(s => String(s.id));
      const newSelectedIds = currentSelectedIds.filter(id =>
        !filteredStudentIds.includes(id)
      );
      const newSelectedStudents = this.data.selectedStudents.filter(student =>
        !filteredStudents.some(s => String(s.id) === String(student.id))
      );

      // 更新filteredStudents中的isSelected属性
      const updatedFilteredStudents = filteredStudents.map(student => ({
        ...student,
        isSelected: false
      }));

      this.setData({
        selectedStudentIds: newSelectedIds,
        selectedStudents: newSelectedStudents,
        filteredStudents: updatedFilteredStudents
      });

    } else {
      // 全选
      const newIds = [...currentSelectedIds];
      const newStudents = [...this.data.selectedStudents];

      filteredStudents.forEach(student => {
        const studentId = String(student.id);
        if (!currentSelectedIds.includes(studentId)) {
          newIds.push(studentId);
          newStudents.push(student);
        }
      });

      // 更新filteredStudents中的isSelected属性
      const updatedFilteredStudents = filteredStudents.map(student => ({
        ...student,
        isSelected: true
      }));

      this.setData({
        selectedStudentIds: newIds,
        selectedStudents: newStudents,
        filteredStudents: updatedFilteredStudents
      });

    }
  },

  /**
   * 切换学生选择状态
   */
  toggleStudentSelection(e) {
    const { student } = e.currentTarget.dataset;

    if (!student || !student.id) {
      console.error('学生数据为空或缺少ID');
      return;
    }

    // 确保ID为字符串类型，避免类型不匹配问题
    const studentId = String(student.id);
    const { selectedStudentIds } = this.data;

    // 确保selectedStudentIds中的所有ID都是字符串类型
    const currentSelectedIds = selectedStudentIds.map(id => String(id));

    let newSelectedIds;
    const index = currentSelectedIds.indexOf(studentId);

    if (index > -1) {
      // 取消选择
      newSelectedIds = currentSelectedIds.filter(id => id !== studentId);

    } else {
      // 添加选择
      newSelectedIds = [...currentSelectedIds, studentId];

    }

    // 更新选中的学生数据
    const newSelectedStudents = this.data.allStudents.filter(s =>
      newSelectedIds.includes(String(s.id))
    );

    // 更新filteredStudents，为每个学生添加isSelected属性
    const updatedFilteredStudents = this.data.filteredStudents.map(student => ({
      ...student,
      isSelected: newSelectedIds.includes(String(student.id))
    }));

    this.setData({
      selectedStudentIds: newSelectedIds,
      selectedStudents: newSelectedStudents,
      filteredStudents: updatedFilteredStudents
    });

    // 验证选中状态
    const isSelected = newSelectedIds.includes(studentId);
    
  },

  /**
   * 🔧 临时测试方法 - 手动调试选中状态
   */
  debugStudentSelection() {

    // 检查每个学生的选中状态
    this.data.filteredStudents.forEach(student => {
      const isSelected = this.data.selectedStudentIds.includes(student.id);
      
    });
    
    // 强制触发一次更新
    this.setData({
      selectedStudentIds: [...this.data.selectedStudentIds],
      _forceUpdate: Date.now()
    });
  },

  /**
   * 确认学生选择
   */
  confirmStudentSelection() {
    // 确保数据类型一致性
    const selectedStudentIds = this.data.selectedStudentIds.map(id => String(id));
    const selectedStudents = this.data.allStudents.filter(student =>
      selectedStudentIds.includes(String(student.id))
    );

    this.setData({
      selectedStudents,
      selectedStudentIds,
      showStudentPopup: false
    });
  },

  /**
   * 移除学生
   */
  removeStudent(e) {
    const { student } = e.currentTarget.dataset;

    if (!student) {
      console.error('学生数据为空');
      return;
    }

    const selectedStudents = this.data.selectedStudents.filter(s => s.id !== student.id);
    const selectedStudentIds = this.data.selectedStudentIds.filter(id => id !== student.id);

    this.setData({
      selectedStudents,
      selectedStudentIds
    });

  },

  /**
   * 选择评语风格
   */
  selectStyle(e) {
    const { style } = e.currentTarget.dataset;
    this.setData({ 'formData.style': style });
  },

  /**
   * 切换重点关注标签
   */
  toggleFocus(e) {
    const { focus } = e.currentTarget.dataset;

    const currentFocus = this.data.formData.focus;

    let newFocus;
    if (currentFocus.includes(focus)) {
      // 如果已选中，则移除
      newFocus = currentFocus.filter(item => item !== focus);

    } else {
      // 如果未选中，则添加
      newFocus = [...currentFocus, focus];

    }

    this.setData({
      'formData.focus': newFocus
    });

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

    // 移除强制关闭逻辑，避免干扰正常的弹窗显示
    // 只在页面初始化时确保状态正确
    if (!this.data.isPageInitialized) {
      this.setData({
        showStudentPopup: false,
        isPopupOperating: false,
        isPageInitialized: true
      });

    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

    // 只在页面首次显示或从其他页面返回时才重置弹窗状态
    // 避免在弹窗正常显示时被意外关闭
    if (!this.data.isPopupOperating && !this.data.showStudentPopup) {

    } else {

    }

    // 如果没有学生数据，重新加载
    if (this.data.allStudents.length === 0) {

      this.loadStudentList().catch(error => {
        console.error('页面显示时加载学生数据失败:', error);
      });
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 获取风格文本
   */
  getStyleText(style) {
    const styleMap = {
      wisdom: '智慧守护型',
      warm: '暖心观察型',
      global: '全局分析型',
      inductive: '归纳推理型'
    };
    return styleMap[style] || '智慧守护型';
  },

  /**
   * 重新生成单条评语
   */
  async regenerateComment(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.generatedComments[index];
    const student = this.data.selectedStudents.find(s => s.id === comment.studentId);

    if (!student) return;

    try {
      wx.showLoading({
        title: '重新生成中...',
        mask: true
      });

      await new Promise(resolve => setTimeout(resolve, 1000));

      const newContent = this.generateCommentContent(student);
      const updatedComments = [...this.data.generatedComments];
      updatedComments[index] = {
        ...comment,
        content: newContent,
        generateTime: new Date().toLocaleString()
      };

      this.setData({ generatedComments: updatedComments });

      wx.hideLoading();
      wx.showToast({
        title: '重新生成成功',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '生成失败',
        icon: 'none'
      });
    }
  },

  /**
   * 编辑评语
   */
  editComment(e) {
    const { index } = e.currentTarget.dataset;
    const comment = this.data.generatedComments[index];

    if (!comment) {
      wx.showToast({
        title: '获取评语信息失败',
        icon: 'none'
      });
      return;
    }

    // 如果评语已保存，跳转到编辑页面
    if (comment._id || comment.id) {
      wx.navigateTo({
        url: `/pages/comment/edit/edit?id=${comment._id || comment.id}`
      });
    } else {
      // 如果评语未保存，显示编辑弹窗
      wx.showModal({
        title: '编辑评语',
        content: '评语尚未保存，请先保存后再编辑',
        showCancel: false
      });
    }
  },

  /**
   * 保存评语
   */
  async saveComments() {
    if (this.data.generatedComments.length === 0) {
      wx.showToast({
        title: '没有可保存的评语',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ saving: true });

      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      // 模拟保存到服务器
      await new Promise(resolve => setTimeout(resolve, 1000));

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    } finally {
      this.setData({ saving: false });
    }
  },

  /**
   * 取消操作
   */
  onCancel() {
    if (this.data.generatedComments.length > 0) {
      wx.showModal({
        title: '确认取消',
        content: '已生成的评语将会丢失，确定要取消吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack();
          }
        }
      });
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 验证生成参数
   */
  validateGenerateParams() {
    const errors = [];

    // 学生选择验证
    if (!this.data.selectedStudents || this.data.selectedStudents.length === 0) {
      errors.push('请至少选择一个学生');
    }

    // 时间范围验证
    if (!this.data.formData.startDate || !this.data.formData.endDate) {
      errors.push('请选择时间范围');
    } else {
      const startDate = new Date(this.data.formData.startDate);
      const endDate = new Date(this.data.formData.endDate);

      if (startDate > endDate) {
        errors.push('开始时间不能晚于结束时间');
      }

      if (endDate > new Date()) {
        errors.push('结束时间不能晚于今天');
      }

      // 检查时间跨度是否合理（不超过1年）
      const daysDiff = (endDate - startDate) / (1000 * 60 * 60 * 24);
      if (daysDiff > 365) {
        errors.push('时间范围不能超过一年');
      }
    }

    // 自定义要求长度验证
    if (this.data.formData.customRequirement && this.data.formData.customRequirement.length > 500) {
      errors.push('自定义要求不能超过500个字符');
    }

    // 选择学生数量限制
    if (this.data.selectedStudents.length > 50) {
      errors.push('单次最多只能为50个学生生成评语');
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      message: errors.length > 0 ? errors.join('\n') : '验证通过'
    };
  },

  /**
   * 🎯 直接调用AI生成评语 - 完全基于数据库提示词
   */
  async generateAIComment(student, formData) {
    try {

      // 1. 获取学生的行为记录数据
      const performanceMaterial = await this.getStudentPerformanceData(student, formData);

      // 2. 直接调用 callDoubaoAPI 云函数
      const callData = {
        style: formData.style || 'warm',
        length: formData.length || 'medium',
        temperature: 0.7,
        max_tokens: 300,
        studentName: student.name,
        performanceMaterial: performanceMaterial
      };

      // 🎯 调用生产云函数
      const response = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'callDoubaoAPI',
          data: callData,
          timeout: 60000,
          success: resolve,
          fail: reject
        });
      });

      if (response.result && response.result.success) {
        // AI生成成功
        const content = response.result.data.content;

        return {
          id: `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          studentId: student.id,
          studentName: student.name,
          className: student.className,
          content: content,
          style: this.getStyleText(formData.style),
          length: this.getLengthText(formData.length),
          generateTime: new Date().toLocaleString(),
          recordCount: student.recordCount || 0,
          isAIGenerated: true
        };
      } else {
        // AI调用失败
        const errorMessage = response.result?.error || response.result?.message || '未知错误';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('🎯 AI生成失败:', error);
      throw error;
    }
  },

  /**
   * 获取学生表现数据
   */
  async getStudentPerformanceData(student, formData) {
    try {
      const cloudService = getCloudService();
      
      // 获取学生在指定时间范围内的行为记录
      const recordsResult = await cloudService.getStudentRecords(student.id, {
        startDate: formData.startDate,
        endDate: formData.endDate
      });

      if (recordsResult.success && recordsResult.data.length > 0) {
        // 将行为记录格式化为文本
        const records = recordsResult.data;
        let performanceText = '';
        
        records.forEach((record, index) => {
          const date = new Date(record.createTime).toLocaleDateString();
          performanceText += `${index + 1}. [${date}] ${record.type || '行为记录'}: ${record.content || record.description}\n`;
        });
        
        return performanceText.trim();
      } else {
        // 没有记录时返回空字符串，让云函数处理
        return '';
      }
    } catch (error) {

      return ''; // 返回空字符串，让云函数处理
    }
  },

  /**
   * 获取提示词模板（Ultra-Think架构升级版）
   * 优先从云端获取动态模板，失败时降级到硬编码版本
   */
  async getPromptTemplate(style) {
    try {
      // 尝试使用模板缓存系统获取动态模板
      const templateCache = require('../../../utils/templateCache')
      console.log(`[模板获取] 开始获取${style}类型的动态模板`)
      
      const template = await templateCache.getTemplate(style)
      
      if (template && template.content) {
        console.log(`[模板获取] 动态模板获取成功: ${style}`)
        console.log(`[模板信息] 来源: ${template.source || 'cloud'}, 版本: ${template.version || 1}`)
        
        // 返回模板内容，统一格式处理
        return typeof template.content === 'string' ? template.content : template.content.toString()
      } else {
        throw new Error('模板内容为空')
      }
      
    } catch (error) {
      console.error(`[模板获取] 动态模板获取失败，使用硬编码模板: ${error.message}`);

      // 降级到硬编码模板
      const hardcodedTemplates = {
        warm: `你是一位温暖亲切的老师，请根据学生的表现生成一段温暖鼓励的评语。`,
        wisdom: `你是一位严谨的教师，请根据学生的表现生成一段正式规范的评语。`,
        global: `你是一位善于激励学生的老师，请根据学生的表现生成一段鼓励性的评语。`,
        inductive: `你是一位细致入微的教师，请根据学生的表现生成一段详细具体的评语。`
      };

      return hardcodedTemplates[style] || hardcodedTemplates.warm;
    }
  },

  /**
   * 解析AI响应
   */
  parseAIResponse(response) {
    try {
      // 解析AI响应
      
      // 适配云函数的返回格式: { data: { content: "评语内容" } }
      if (response && response.data && response.data.content) {
        const content = response.data.content.trim();
        // 内容解析成功
        return content;
      }
      // 适配豆包AI原始响应格式
      else if (response && response.choices && response.choices[0]) {
        const content = response.choices[0].message.content.trim();

        return content;
      } 
      // 备用格式
      else if (response && response.content) {
        const content = response.content.trim();

        return content;
      } 
      // 字符串格式
      else if (typeof response === 'string') {
        const content = response.trim();

        return content;
      } else {
        console.error('无法解析的AI响应格式:', response);
        throw new Error('无法解析AI响应格式');
      }
    } catch (error) {
      console.error('解析AI响应失败:', error, '响应数据:', response);
      throw new Error('AI响应格式错误');
    }
  },

  /**
   * 获取长度文本
   */
  getLengthText(length) {
    const lengthTexts = {
      short: '简洁版',
      medium: '标准版',
      long: '详细版'
    };
    return lengthTexts[length] || '标准版';
  },

  /**
   * 检查哪些学生缺少行为记录
   */
  async checkStudentsWithoutRecords(students) {
    const studentsWithoutRecords = [];
    
    for (let student of students) {
      try {
        // 使用正确的方法名和参数
        const performanceMaterial = await this.getStudentPerformanceData(student, this.data.formData);
        
        if (!performanceMaterial || performanceMaterial.trim() === '') {

          studentsWithoutRecords.push(student);
        } else {
          
        }
      } catch (error) {
        console.error(`[记录检查] 学生${student.name}记录获取失败:`, error);
        // 获取记录失败的学生也算作缺少记录
        studentsWithoutRecords.push(student);
      }
    }

    return studentsWithoutRecords;
  },

  /**
   * 显示通用评语授权弹窗
   */
  showGenericCommentAuthModal(studentsWithoutRecords) {
    return new Promise((resolve) => {
      const studentNames = studentsWithoutRecords.map(s => s.name).join('、');
      const message = `检测到以下学生暂无行为记录：\n\n${studentNames}\n\n系统将为这些学生生成基于通用模板的积极正面评语。评语内容将重点关注学习态度、成长潜力和未来期望。\n\n是否同意使用通用评语模板？`;
      
      wx.showModal({
        title: '通用评语授权确认',
        content: message,
        confirmText: '同意使用',
        cancelText: '取消生成',
        confirmColor: '#5470C6',
        success: (res) => {
          if (res.confirm) {
            // 用户同意，记录授权信息
            wx.setStorageSync('genericCommentAuthTime', Date.now());
            resolve(true);
          } else {
            resolve(false);
          }
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  /**
   * 成功生成后的分享
   */
  async shareSuccess() {

    try {
      const { generateResult } = this.data;
      
      if (!generateResult || !generateResult.comments || generateResult.comments.length === 0) {
        wx.showToast({
          title: '暂无生成结果',
          icon: 'none'
        });
        return;
      }

      // 获取第一个生成的评语作为代表
      const firstComment = generateResult.comments[0];
      
      // 准备分享内容
      const shareContent = {
        studentName: firstComment.studentName || '同学们',
        commentContent: firstComment.content || '',
        score: '9.2',
        style: this.data.formData.style || '专业评语',
        teacherName: this.getTeacherName(),
        totalCount: generateResult.successCount || generateResult.comments.length
      };

      // 多层级安全分享
      if (smartShare && typeof smartShare === 'function') {
        await smartShare(shareContent, 'achievement', {
          canvasId: 'shareCanvas'
        });
      } else if (emergencyShare && typeof emergencyShare === 'function') {
        // 第二级降级：紧急分享
        emergencyShare(shareContent);
      } else {
        // 最终降级：基础分享
        this.fallbackShare(shareContent);
      }

    } catch (error) {
      console.error('[AI生成] 分享成功结果失败:', error);
      wx.showToast({
        title: '分享失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 炫耀分享 - 展示AI生成评语成果
   */
  async shareAchievement() {

    try {
      const { generatedComment, selectedStudent, formData } = this.data;
      
      if (!generatedComment || !selectedStudent) {
        wx.showToast({
          title: '请先生成评语',
          icon: 'none'
        });
        return;
      }

      // 准备分享内容
      const shareContent = {
        studentName: selectedStudent.name || '某同学',
        commentContent: generatedComment,
        score: this.data.resultScore || '9.2',
        style: formData.style || '专业评语',
        teacherName: this.getTeacherName()
      };

      // 多层级安全分享
      if (smartShare && typeof smartShare === 'function') {
        await smartShare(shareContent, 'achievement', {
          canvasId: 'shareCanvas'
        });
      } else if (emergencyShare && typeof emergencyShare === 'function') {
        // 第二级降级：紧急分享
        emergencyShare(shareContent);
      } else {
        // 最终降级：基础分享
        this.fallbackShare(shareContent);
      }

    } catch (error) {
      console.error('[AI生成] 炫耀分享失败:', error);
      wx.showToast({
        title: '分享失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 获取教师姓名
   */
  getTeacherName() {
    try {
      const userInfo = wx.getStorageSync('userInfo') || app.globalData.userInfo;
      return userInfo?.name || userInfo?.nickName || '老师';
    } catch (error) {
      return '老师';
    }
  },

  /**
   * 降级分享方案 - 确保分享功能始终可用
   */
  fallbackShare(shareContent) {
    try {
      const shareText = `🎉 我用AI评语助手为${shareContent.studentName}生成了专业评语！\n\n📝 评语内容：${shareContent.commentContent.substring(0, 100)}${shareContent.commentContent.length > 100 ? '...' : ''}\n\n⭐ 评分：${shareContent.score}分\n👩‍🏫 ${shareContent.teacherName}\n\n💡 3分钟生成专业评语，推荐给所有老师！`;

      wx.setClipboardData({
        data: shareText,
        success: () => {
          wx.showModal({
            title: '✅ 分享内容已复制',
            content: '评语内容已复制到剪贴板！\n\n您可以：\n• 粘贴到微信群或朋友圈\n• 分享给其他老师\n• 发布到教育论坛\n\n让更多老师体验AI评语的便利！',
            showCancel: false,
            confirmText: '知道了'
          });
        },
        fail: () => {
          wx.showToast({
            title: '复制失败，请手动分享',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      console.error('[降级分享] 执行失败:', error);
      wx.showToast({
        title: '分享功能暂不可用',
        icon: 'none'
      });
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '🤖 AI评语助手 - 3分钟生成专业评语',
      path: '/pages/comment/generate/generate?from=share',
      imageUrl: '' // 可以添加分享图片
    };
  },

  /**
   * 构建新版AI提示词
   */
  buildNewPrompt(aiRequest) {
    const { studentInfo, behaviorTags, prompt, style } = aiRequest;

    let basePrompt = `你是一位专业的班主任老师，请根据以下信息为学生生成评语：

学生信息：
- 姓名：${studentInfo.name}
- 性别：${studentInfo.gender === 'female' ? '女' : '男'}
- 班级：${studentInfo.grade}

学生表现材料：
${prompt}

行为标签：${behaviorTags.join('、')}

评语风格：${this.getStyleDescription(style)}

请生成一份个性化的学生评语，要求：
1. 语言温暖、具体、有建设性
2. 结合具体的行为表现
3. 给出鼓励和建议
4. 字数控制在100-150字
5. 直接生成评语内容，不要包含任何标识或前缀

示例格式：${studentInfo.name}同学在本学期...`;

    return basePrompt;
  },

  /**
   * 获取风格描述
   */
  getStyleDescription(style) {
    const styleMap = {
      'warm': '暖心观察型模板 - 重点突出学生优点，语言温暖有爱',
      'wisdom': '智慧守护型模板 - 客观描述表现，语言规范严谨',
      'global': '全局分析型模板 - 以鼓励为主，激发学生潜能',
      'inductive': '归纳推理型模板 - 内容详实，分析深入，全面细致'
    };
    return styleMap[style] || styleMap['warm'];
  },

  /**
   * 提取行为标签
   */
  extractBehaviorTags(performanceMaterial) {
    if (!performanceMaterial || performanceMaterial.trim() === '') {
      return ['表现良好'];
    }

    // 简单的关键词提取逻辑
    const tags = [];
    const text = performanceMaterial.toLowerCase();

    if (text.includes('积极') || text.includes('主动')) tags.push('积极主动');
    if (text.includes('认真') || text.includes('仔细')) tags.push('认真负责');
    if (text.includes('帮助') || text.includes('友善')) tags.push('乐于助人');
    if (text.includes('进步') || text.includes('提高')) tags.push('不断进步');
    if (text.includes('创新') || text.includes('想法')) tags.push('富有创意');
    if (text.includes('团队') || text.includes('合作')) tags.push('团队合作');

    return tags.length > 0 ? tags : ['表现良好'];
  },

  /**
   * 解析新版AI响应
   */
  parseNewAIResponse(response) {
    try {
      
      // 处理微信云函数调用的包装格式
      if (response && response.result) {
        const result = response.result;
        
        if (result.success && result.data) {
          return result.data.content || result.data.choices?.[0]?.message?.content || '生成失败';
        } else if (result.success && result.choices && result.choices[0]) {
          // 处理直接返回choices格式
          return result.choices[0].message?.content || result.choices[0].text || '生成失败';
        } else {
          throw new Error(result.error || result.message || '生成失败');
        }
      }
      
      // 处理直接的AI响应格式（从错误日志看到的格式）
      if (response && response.success && response.content) {

        return response.content;
      }
      
      // 处理直接的云函数响应格式
      if (response && response.success && response.data) {
        return response.data.content || '生成失败';
      }
      
      // 处理原始AI API响应格式
      if (response && response.choices && response.choices[0]) {
        return response.choices[0].message?.content || response.choices[0].text || '生成失败';
      }
      
      // 处理简单字符串响应
      if (typeof response === 'string') {
        return response;
      }

      throw new Error('未知响应格式');
    } catch (error) {
      console.error('解析AI响应失败:', error);
      console.error('响应数据结构:', response);
      return '评语生成失败，请重试';
    }
  }
});